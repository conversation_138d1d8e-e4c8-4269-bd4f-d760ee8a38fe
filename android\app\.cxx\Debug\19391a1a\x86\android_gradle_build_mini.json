{"buildFiles": ["C:\\Users\\<USER>\\AppData\\Local\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Daftar materi\\PMOB bang\\tugaspraktikum\\android\\app\\.cxx\\Debug\\19391a1a\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Daftar materi\\PMOB bang\\tugaspraktikum\\android\\app\\.cxx\\Debug\\19391a1a\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}